#!/bin/bash

# <PERSON>ript to remove the claim-app key from SSH agent and prevent it from being re-added
echo "Removing claim-app key from SSH agent..."

# First, let's check what keys are currently in the agent
echo "Current keys in SSH agent:"
ssh-add -l

# Remove all keys from the SSH agent
echo "Removing all keys from SSH agent..."
ssh-add -D

# Kill any existing SSH agent processes
echo "Killing SSH agent processes..."
pkill ssh-agent

# Check if the GNOME keyring SSH component is running
echo "Checking for GNOME keyring SSH component..."
if pgrep -f "gnome-keyring-daemon.*--components=ssh" > /dev/null; then
    echo "GNOME keyring SSH component is running. Disabling it..."
    # Create a backup of the autostart file if it exists
    if [ -f ~/.config/autostart/gnome-keyring-ssh.desktop ]; then
        cp ~/.config/autostart/gnome-keyring-ssh.desktop ~/.config/autostart/gnome-keyring-ssh.desktop.bak
        echo "Hidden=true" >> ~/.config/autostart/gnome-keyring-ssh.desktop
        echo "GNOME keyring SSH component disabled."
    fi
fi

# Check if the systemd SSH agent service is enabled
echo "Checking for systemd SSH agent service..."
if systemctl --user is-enabled ssh-agent.service &>/dev/null; then
    echo "Systemd SSH agent service is enabled. Disabling it..."
    systemctl --user disable ssh-agent.service
    echo "Systemd SSH agent service disabled."
fi

# Create a backup of the claim-app key if it exists and you haven't already done so
if [ -f "/home/<USER>/Desktop/DS/tradesman/claim-app-frontend-key-prod.pem" ]; then
    echo "Creating backup of claim-app key..."
    mkdir -p ~/key-backups
    mv "/home/<USER>/Desktop/DS/tradesman/claim-app-frontend-key-prod.pem" ~/key-backups/claim-app-frontend-key-prod.pem.bak
    echo "Key backed up to ~/key-backups/claim-app-frontend-key-prod.pem.bak"
else
    echo "Claim-app key file not found at the expected location."
fi

# Check if the key is in the current directory
if [ -f "./claim-app-frontend-key-prod.pem" ]; then
    echo "Found key in current directory. Moving it to backup..."
    mkdir -p ~/key-backups
    mv "./claim-app-frontend-key-prod.pem" ~/key-backups/claim-app-frontend-key-prod.pem.bak2
    echo "Key backed up to ~/key-backups/claim-app-frontend-key-prod.pem.bak2"
fi

echo "Done. You may need to log out and log back in for changes to take effect."
