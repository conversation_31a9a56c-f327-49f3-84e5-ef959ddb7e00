#!/bin/bash

# <PERSON><PERSON>t to remove the claim-app key from SSH agent
echo "Removing claim-app key from SSH agent..."

# Try to remove the key by its path (even though it's moved)
ssh-add -d /home/<USER>/Desktop/DS/tradesman/claim-app-frontend-key-prod.pem 2>/dev/null

# Try to remove the key by its fingerprint
ssh-add -d ~/.ssh/id_rsa 2>/dev/null

# List remaining keys
echo "Remaining keys in SSH agent:"
ssh-add -l

echo "Done."
